import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import UploadFile from '../UploadFile.vue';
import { validateFile, parseFileUrl, formatFileSize } from '../utils/fileUtils';

// Mock dependencies
vi.mock('@/hooks/web/useI18n', () => ({
  useI18n: () => ({
    t: (key: string, params?: any[]) => {
      const translations: Record<string, string> = {
        'component.upload.upload': '上传',
        'component.upload.uploading': '上传中',
        'component.upload.choose': '选择文件',
        'component.upload.accept': '支持{0}格式',
        'component.upload.preview': '预览',
        'component.upload.download': '下载',
        'component.upload.del': '删除',
        'component.upload.uploadSuccess': '上传成功',
        'component.upload.uploadError': '上传失败',
        'component.upload.acceptUpload': '只能上传{0}格式文件',
        'component.upload.maxSizeMultiple': '只能上传不超过{0}MB的文件!',
      };
      let result = translations[key] || key;
      if (params) {
        params.forEach((param, index) => {
          result = result.replace(`{${index}}`, param);
        });
      }
      return result;
    },
  }),
}));

vi.mock('@/hooks/component/useFormItem', () => ({
  useRuleFormItem: () => [{ value: '' }],
}));

vi.mock('@/api/admin/file', () => ({
  apiGetUploadUrl: vi.fn(),
  apiUploadFileToSignedUrl: vi.fn(),
}));

vi.mock('@/utils/other', () => ({
  sleep: vi.fn(),
}));

vi.mock('@/utils/photoCompress', () => ({
  PhotoCompress: vi.fn().mockImplementation(() => ({
    compress: vi.fn(),
  })),
}));

vi.mock('@/utils/file/download', () => ({
  downloadByUrl: vi.fn(),
}));

describe('UploadFile', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly with default props', () => {
    const wrapper = mount(UploadFile);
    
    expect(wrapper.find('.upload-file-container').exists()).toBe(true);
    expect(wrapper.find('.upload-file-trigger').exists()).toBe(true);
    expect(wrapper.find('.upload-file-button').exists()).toBe(true);
  });

  it('hides upload trigger in preview mode', () => {
    const wrapper = mount(UploadFile, {
      props: {
        preview: true,
      },
    });
    
    expect(wrapper.find('.upload-file-trigger').exists()).toBe(false);
  });

  it('shows disabled state correctly', () => {
    const wrapper = mount(UploadFile, {
      props: {
        disabled: true,
      },
    });
    
    expect(wrapper.find('.upload-file-container--disabled').exists()).toBe(true);
  });

  it('displays file list when value is provided', async () => {
    const wrapper = mount(UploadFile, {
      props: {
        value: 'https://example.com/test.pdf',
        valueType: 'string',
      },
    });
    
    await nextTick();
    expect(wrapper.find('.upload-file-list').exists()).toBe(true);
    expect(wrapper.find('.upload-file-item').exists()).toBe(true);
  });

  it('handles multiple files correctly', async () => {
    const wrapper = mount(UploadFile, {
      props: {
        value: ['https://example.com/test1.pdf', 'https://example.com/test2.pdf'],
        valueType: 'array',
      },
    });
    
    await nextTick();
    expect(wrapper.findAll('.upload-file-item')).toHaveLength(2);
  });

  it('shows correct file types in accept hint', () => {
    const wrapper = mount(UploadFile, {
      props: {
        fileType: ['PDF', 'DOC'],
      },
    });
    
    const acceptText = wrapper.find('.upload-accept-text');
    expect(acceptText.text()).toContain('PDF、DOC');
  });

  it('triggers file input click when upload button is clicked', async () => {
    const wrapper = mount(UploadFile);
    const fileInput = wrapper.find('input[type="file"]');
    const clickSpy = vi.spyOn(fileInput.element, 'click');
    
    await wrapper.find('.upload-file-button').trigger('click');
    expect(clickSpy).toHaveBeenCalled();
  });
});

describe('File Utils', () => {
  describe('validateFile', () => {
    it('validates file type correctly', () => {
      const file = new File(['content'], 'test.pdf', { type: 'application/pdf' });
      const acceptList = ['application/pdf'];
      
      const result = validateFile(file, acceptList);
      expect(result.valid).toBe(true);
    });

    it('rejects files with comma in filename', () => {
      const file = new File(['content'], 'test,file.pdf', { type: 'application/pdf' });
      const acceptList = ['application/pdf'];
      
      const result = validateFile(file, acceptList);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('逗号');
    });

    it('rejects unsupported file types', () => {
      const file = new File(['content'], 'test.exe', { type: 'application/exe' });
      const acceptList = ['application/pdf'];
      
      const result = validateFile(file, acceptList);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('UNSUPPORTED_FILE_TYPE');
    });
  });

  describe('parseFileUrl', () => {
    it('parses file URL correctly', () => {
      const url = 'https://example.com/bucket/test.pdf?filename=document.pdf';
      const result = parseFileUrl(url);
      
      expect(result.url).toBe(url);
      expect(result.fileName).toBe('document.pdf');
      expect(result.bucketName).toBe('bucket');
      expect(result.suffix).toBe('PDF');
    });

    it('handles invalid URLs gracefully', () => {
      const url = 'invalid-url';
      const result = parseFileUrl(url);
      
      expect(result.fileName).toBe('文件解析错误');
      expect(result.url).toBe('');
    });
  });

  describe('formatFileSize', () => {
    it('formats file size correctly', () => {
      expect(formatFileSize(0)).toBe('0 B');
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1048576)).toBe('1 MB');
      expect(formatFileSize(1073741824)).toBe('1 GB');
    });

    it('handles decimal places correctly', () => {
      expect(formatFileSize(1536)).toBe('1.5 KB');
      expect(formatFileSize(1572864)).toBe('1.5 MB');
    });
  });
});
