import { BasicColumn, FormSchema } from '@/components/Table';
import { previewImage, showToBadge } from '@/components/RenderVnode';
import { useMapWithI18n } from '@/hooks/web/useOnlineI18n';
import { AppStatusList } from '@/maps/prMaps';
import { apiInfoList } from '@/api/op/pr';
import { transformRangePicker } from '@/utils/formFn';

export const basicColumns: BasicColumn[] = [
  {
    title: 'LOGO',
    dataIndex: 'logo',
    width: 80,
    customRender: ({ text }) => {
      return previewImage(text, 'miniTable');
    },
  },
  {
    title: '应用名称',
    dataIndex: 'name',
    width: 150,
  },
  {
    title: '分类',
    dataIndex: 'typeDesc',
    width: 120,
  },

  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }) => {
      return showToBadge({
        text: record.status,
        arr: useMapWithI18n(AppStatusList),
      });
    },
  },
  {
    title: '最后更新时间',
    dataIndex: 'updateTime',
    width: 180,
  },
];

export const searchSchema = (): FormSchema[] => [
  {
    field: 'name',
    label: '应用名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入应用名称',
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      options: useMapWithI18n(AppStatusList),
    },
  },
  {
    field: 'productId',
    label: '产品标识',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请输入产品标识',
      api: apiInfoList,
      labelField: 'model',
      valueField: 'id',
    },
    colProps: { span: 12 },
  },
  {
    field: '[beginTime,endTime]',
    label: '最后更新时间',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['最后更新时间起始', '最后更新时间截止'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    valueFormat: transformRangePicker,
  },
];

// 分类表单 schema
export const basicCategorySchema: FormSchema[] = [
  {
    field: 'name',
    fields: ['id', 'pid'],
    label: '分类名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入分类名称',
    },
  },
];
